import { appConfig } from '@/config/environment';
import { aiService as supabaseAIService } from './aiService';
import { volcanoService } from './volcanoService';

export interface AIService {
  hasValidApiKey(): boolean;
  setApiKey(apiKey: string): void;
  generateTopics(domain: string): Promise<string[]>;
  optimizeContent(originalText: string, platform?: string, style?: string): Promise<string>;
  generateVideoScript(originalText: string, platform?: string, style?: string): Promise<string>;
  generateContentByDomain(domain: string): Promise<string[]>;
  generateContentByTopic(topic: string): Promise<string[]>;
  generateContentByTopicStream(topic: string, onContent: (content: string) => void): Promise<string>;
  popularizeContent(content: string): Promise<string>;
  extractDocumentSummary(content: string): Promise<any>;
}

export const getAIService = (): AIService => {
  console.log(`🔧 AI服务配置:`, {
    provider: appConfig.apiProvider,
    volcanoUrl: import.meta.env.VITE_VOLCANO_DOUBAO_AI_URL,
    envProvider: import.meta.env.VITE_API_PROVIDER
  });

  if (appConfig.apiProvider === 'volcano') {
    console.log('✅ 选择火山引擎服务');
    return volcanoService;
  } else {
    console.log('✅ 选择Supabase服务');
    return supabaseAIService;
  }
};

// 默认AI服务实例
export const aiService = getAIService();