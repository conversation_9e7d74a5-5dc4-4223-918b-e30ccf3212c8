// 测试火山引擎API的简单脚本
// 使用Node.js 18+的内置fetch API

async function testVolcanoAPI() {
  const url = 'https://sd1mdl74gijnb7ntvsi00.apigateway-cn-guangzhou.volceapi.com/ai-service';
  
  const testCases = [
    {
      name: '生成选题',
      action: 'generateTopics',
      data: { domain: '法律咨询' }
    },
    {
      name: '生成内容',
      action: 'generateContent',
      data: { domain: '法律咨询' }
    },
    {
      name: '按选题生成内容',
      action: 'generateContentByTopic',
      data: { topic: '劳动合同纠纷处理指南' }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🧪 测试: ${testCase.name}`);
    console.log(`📡 Action: ${testCase.action}`);
    console.log(`📦 Data:`, testCase.data);
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: testCase.action,
          data: testCase.data,
          authorization: 'test-token' // 使用测试token
        })
      });

      console.log(`📊 状态码: ${response.status}`);
      
      const responseText = await response.text();
      console.log(`📄 原始响应:`, responseText.substring(0, 200) + '...');
      
      try {
        const result = JSON.parse(responseText);
        console.log(`✅ JSON解析成功`);
        console.log(`📦 响应结构:`, Object.keys(result));
        
        if (result.statusCode && result.body) {
          const body = typeof result.body === 'string' ? JSON.parse(result.body) : result.body;
          console.log(`📦 Body结构:`, Object.keys(body));
          
          if (body.result) {
            console.log(`✅ 结果类型:`, typeof body.result);
            console.log(`✅ 是否为数组:`, Array.isArray(body.result));
            if (Array.isArray(body.result)) {
              console.log(`✅ 数组长度:`, body.result.length);
              console.log(`✅ 第一项预览:`, body.result[0]?.substring(0, 100) + '...');
            } else {
              console.log(`✅ 内容预览:`, body.result?.substring(0, 100) + '...');
            }
          }
        }
      } catch (e) {
        console.error(`❌ JSON解析失败:`, e.message);
      }
      
    } catch (error) {
      console.error(`❌ 请求失败:`, error.message);
    }
    
    console.log('─'.repeat(50));
  }
}

testVolcanoAPI().catch(console.error);
